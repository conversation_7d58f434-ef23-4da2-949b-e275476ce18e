<template>
  <div class="bg-bac flex h-full w-full justify-center overflow-y-auto">
    <div class="my-5 px-2 px-5 pt-5 rounded-md bg-white">
      <el-form label-width="100px" label-position="left" class="flex flex-col gap-5 md:flex-row md:gap-8">
        <div class="w-full md:w-auto">
          <el-form-item label="项目名称">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="form.desc" type="textarea" />
          </el-form-item>
          <el-form-item label="付费账户">
            <AccountSelect v-model="form.account" />
          </el-form-item>
          <el-form-item label="项目ID">
            <ProjectId v-model="form.id" />
          </el-form-item>
          <el-form-item label="地区">
            <RegionCascader v-model="form.region" />
          </el-form-item>
          <el-form-item label="创建时间">
            <div>{{ form.createTime }}</div>
          </el-form-item>
          <el-form-item label="修改时间">
            <div>{{ form.updateTime }}</div>
          </el-form-item>
          <el-form-item label="标签">
            <TagInput v-model="form.tags" />
          </el-form-item>

          <el-form-item label="特性">
            <FeatureList style="width: 320px" v-model="form.features" />
          </el-form-item>

          <el-form-item label="总数据量">
            <div class="flex gap-2">
              <div class="text-xl">28.8GB</div>
              <div class="text-tip">该项目贡献了27GB/28.8GB数据</div>
            </div>
          </el-form-item>
        </div>

        <div class="w-full md:w-250px">
          <!-- 权限设置 -->
          <el-form-item label="访问权限">
            <el-select v-model="form.access">
              <el-option label="允许" value="allow" />
              <el-option label="私有" value="private" />
            </el-select>
          </el-form-item>
          <!-- 权限控制 -->
          <el-form-item label="复制权限">
            <el-select v-model="form.copyPermission">
              <el-option label="允许" value="allow" />
              <el-option label="禁止" value="deny" />
            </el-select>
          </el-form-item>
          <el-form-item label="删除权限">
            <el-select v-model="form.deletePermission">
              <el-option label="允许" value="allow" />
              <el-option label="禁止" value="deny" />
            </el-select>
          </el-form-item>
          <el-form-item label="下载权限">
            <el-select v-model="form.downloadPermission">
              <el-option label="允许" value="allow" />
              <el-option label="禁止" value="deny" />
            </el-select>
          </el-form-item>
          <!-- 贡献与数据保护 -->
          <div class="pt-4 border-t border-#E4E7ED">
            <el-form-item label="贡献">
              <el-select v-model="form.contribute">
                <el-option label="贡献数据" value="data" />
                <el-option label="不贡献数据" value="nodata" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据保护">
              <el-switch v-model="form.protect" active-text="开启" inactive-text="关闭" />
            </el-form-item>
          </div>
          <div class="mb-5 pt-4 border-t border-#E4E7ED md:mb-0">
            <div class="mb-2">Administration</div>
            <el-button type="danger">删除项目</el-button>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { CopyDocument, Delete, Plus } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import RegionCascader from '@/components/RegionCascader.vue';
  import AccountSelect from '@/components/AccountSelect.vue';
  import ProjectId from '@/components/ProjectId.vue';
  import FeatureList from '@/components/FeatureList.vue';
  import TagInput from '@/components/TagInput.vue';

  const form = ref({
    name: '我的项目1',
    desc: '',
    account: 'admin',
    id: 'PRJ-F1k8yr9sa73',
    region: [],
    createTime: '2023.10.27 7:42',
    updateTime: '2023.10.27 16:56',
    tags: ['demo'],
    access: 'allow',
    contribute: 'data',
    protect: false,
    copyPermission: 'allow',
    deletePermission: 'allow',
    downloadPermission: 'allow',
    features: [],
  });

  const copyProjectId = () => {
    navigator.clipboard
      .writeText(form.value.id)
      .then(() => {
        ElMessage.success('项目ID已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败');
      });
  };

  const handleDeleteFeature = (index: number) => {
    form.value.features.splice(index, 1);
  };
</script>

<style lang="scss" scoped>
  @media screen and (max-width: 1024px) {
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    :deep(.el-form-item__label) {
      padding-right: 12px;
    }
  }
</style>
