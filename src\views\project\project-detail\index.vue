<template>
  <div class="h-full flex flex-col">
    <!-- 顶部导航栏 -->
    <div class="flex items-center pr-5">
      <h2 class="w-280px flex items-center gap-2 pl-5 text-xl font-bold">
        <el-icon><Briefcase /></el-icon>
        <div class="line-clamp-1">项目名称</div>
      </h2>

      <el-menu class="relative" mode="horizontal" :ellipsis="false" :router="true" :default-active="activeIndex">
        <el-menu-item index="/project/detail/settings">设置</el-menu-item>
        <el-menu-item index="/project/detail/manage">管理</el-menu-item>
        <el-menu-item index="/project/detail/jobs">
          作业
          <span
            v-if="runningJobsCount > 0"
            class="ml-1 flex items-center justify-center rounded-full bg-p px-2 py-1 text-xs text-white"
          >
            {{ runningJobsCount }}
          </span>
        </el-menu-item>
        <el-menu-item index="/project/detail/visualization">可视化</el-menu-item>
      </el-menu>
    </div>
    <router-view />
  </div>
</template>

<script setup lang="ts">
  import { Briefcase } from '@element-plus/icons-vue';
  import { useRoute } from 'vue-router';
  import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
  import jobsService from '@/api/jobs';

  const route = useRoute();
  const activeIndex = ref(route.path);
  const runningJobsCount = ref(0);

  // 获取运行中的作业数量
  const fetchRunningJobsCount = () => {
    runningJobsCount.value = jobsService.getRunningJobsCount();
  };

  // 定时刷新运行中的作业数量
  let timer: number | null = null;

  onMounted(() => {
    fetchRunningJobsCount();
    // 每30秒刷新一次数据
    timer = window.setInterval(fetchRunningJobsCount, 30000);
  });

  onBeforeUnmount(() => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  });

  watch(
    route,
    (newRoute) => {
      // 根据路径前缀设置激活的菜单项
      if (newRoute.path.startsWith('/project/detail/jobs')) {
        activeIndex.value = '/project/detail/jobs';
      } else {
        activeIndex.value = newRoute.path;
      }
    },
    { immediate: true }
  );
</script>

<style scoped>
  .project-detail {
    height: 100%;
  }

  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover,
  .el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
    background-color: transparent;
  }

  :deep(.el-menu) {
    border-bottom: none !important;
  }
</style>
