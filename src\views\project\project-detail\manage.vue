<template>
  <div class="h-0 flex flex-1 bg-bac">
    <!-- 左侧目录树 -->
    <div class="directory-tree overflow-y-auto bg-white">
      <el-tree
        :data="directoryData"
        :props="defaultProps"
        node-key="id"
        :default-expanded-keys="firstLevelKeys"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <el-icon>
              <template v-if="!data.children || data.children.length === 0">
                <Folder />
              </template>
              <template v-else>
                <template v-if="node.level === 1">
                  <Briefcase />
                </template>
                <template v-else>
                  <Folder v-if="!node.expanded" />
                  <FolderOpened v-else />
                </template>
              </template>
            </el-icon>
            <span>{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 右侧内容区域 -->
    <div class="m-5 w-0 flex flex-1 flex-col rounded-md bg-white px-5 pb-2">
      <div class="flex items-center pb-3 pt-4">
        <!-- <el-breadcrumb separator=">">
          <el-breadcrumb-item>
            <router-link to="/project" class="text-p!">所有项目</router-link>
          </el-breadcrumb-item>
          <el-breadcrumb-item>项目名称</el-breadcrumb-item>
        </el-breadcrumb> -->

        <div class="flex gap-3">
          <el-dropdown trigger="hover" @command="handleDropdownCommand">
            <el-button type="default">
              新建<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="folder">
                  <el-icon><folder /></el-icon>新建文件夹
                </el-dropdown-item>
                <el-dropdown-item command="upload">
                  <el-icon><upload /></el-icon>上传数据
                </el-dropdown-item>
                <el-dropdown-item command="workflow">
                  <div class="i-mdi:workflow mr-2"></div>
                  新建工作流
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary">分析</el-button>
        </div>

        <div class="ml-auto flex gap-4">
          <div>
            <el-select v-model="filters.folder" style="width: 150px">
              <el-option
                v-for="item in folderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>

          <div>
            <el-select v-model="filters.type" placeholder="所有类型">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>

          <div>
            <el-input
              v-model="filters.name"
              placeholder="请输入关键字搜索"
              style="width: 300px"
              clearable
              :suffix-icon="Search"
            ></el-input>
          </div>
        </div>
      </div>

      <!-- 文件列表表格 -->
      <div class="h-0 flex-1">
        <el-table class="c-table-header" :data="fileList" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="名称" min-width="200">
            <template #default="{ row }">
              <div class="file-name group relative cursor-pointer hover:text-p" @click="handleEditFile(row)">
                <el-icon v-if="row.type === 'folder'"><Folder /></el-icon>
                <el-icon v-else-if="row.type === 'image'"><Picture /></el-icon>
                <el-icon v-else-if="row.type === 'document'"><Document /></el-icon>
                <el-icon v-else-if="row.type === 'archive'"><FolderDelete /></el-icon>
                <span>{{ row.name }}</span>
                <el-icon v-if="row.type !== 'folder'" size="20px" class="edit-icon opacity-0 group-hover:opacity-100">
                  <Edit />
                </el-icon>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="size" label="大小" />
          <el-table-column prop="modifiedTime" label="修改时间" width="180" />
          <el-table-column prop="status" label="状态" />
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <!-- 新建文件夹对话框 -->
  <el-dialog v-model="newFolderDialogVisible" title="新建文件夹" width="500px">
    <el-form :model="newFolderForm" :rules="newFolderRules" ref="newFolderFormRef" @submit.prevent>
      <el-form-item label="文件夹名称" prop="name">
        <el-input v-model="newFolderForm.name" placeholder="请输入文件夹名称" @keyup.enter="handleCreateFolder" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" @click="newFolderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateFolder">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 上传数据对话框 -->
  <el-dialog v-model="uploadDialogVisible" title="上传数据" width="600px">
    <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" @submit.prevent label-width="100px">
      <el-form-item label="数据类型" prop="type">
        <el-select v-model="uploadForm.type" placeholder="请选择数据类型" style="width: 100%">
          <el-option
            v-for="item in typeOptions.filter((item) => item.value !== 'all' && item.value !== 'folder')"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件" prop="files">
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="uploadForm.files"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">支持单个或批量上传，文件大小不超过10GB</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">开始上传</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 文件编辑对话框 -->
  <FileEditDialog v-model="fileEditDialogVisible" :file-info="currentFile" @success="fetchData()" />
</template>

<script setup lang="ts">
  import {
    ArrowDown,
    Folder,
    Upload,
    Picture,
    Document,
    FolderDelete,
    FolderOpened,
    Briefcase,
    UploadFilled,
    Edit,
    Search,
  } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import type { FormInstance, UploadInstance, UploadProps, UploadUserFile } from 'element-plus';
  import FileEditDialog from './components/FileEditDialog.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  // 定义文件项的类型
  interface FileItem {
    name: string;
    type: string;
    size: string;
    modifiedTime: string;
    status: string;
  }

  // 目录树数据
  const directoryData = ref([
    {
      id: 1,
      label: '项目名称',
      children: [
        {
          id: 2,
          label: '文件夹1',
          children: [
            {
              id: 21,
              label: '文件夹1-1',
              children: [],
            },
            {
              id: 22,
              label: '文件夹1-2',
              children: [],
            },
          ],
        },
        {
          id: 3,
          label: '文件夹2',
          children: [],
        },
      ],
    },
  ]);

  // 获取第一级节点的 key
  const firstLevelKeys = computed(() => {
    return directoryData.value.map((item) => item.id);
  });

  const defaultProps = {
    children: 'children',
    label: 'label',
  };

  const filters = reactive({
    folder: 'current',
    name: '',
    id: '',
    type: 'all',
  });

  const folderOptions = [
    { label: '仅当前文件夹', value: 'current' },
    { label: '包括下级文件夹', value: 'include' },
  ];

  const typeOptions = [
    { label: '所有类型', value: 'all' },
    { label: 'WES', value: 'WES' },
    { label: 'RNA', value: 'RNA' },
    { label: 'WGS', value: 'WGS' },
    { label: '测序', value: '测序' },
    { label: '分析', value: '分析' },
  ];

  // 文件列表数据
  const fileList = ref<FileItem[]>([]);
  const loading = ref(false);

  // 分页相关
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);

  // 获取数据
  async function fetchData() {
    try {
      loading.value = true;
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 300));

      // 模拟数据
      fileList.value = [
        {
          name: '文件夹1',
          type: 'folder',
          size: '-',
          modifiedTime: '2024-03-20 10:00:00',
          status: '正常',
        },
        {
          name: '文件夹2',
          type: 'folder',
          size: '-',
          modifiedTime: '2024-03-20 09:30:00',
          status: '正常',
        },
        {
          name: 'WES数据1',
          type: 'WES',
          size: '1.2GB',
          modifiedTime: '2024-03-20 08:15:00',
          status: '正常',
        },
        {
          name: 'RNA数据1',
          type: 'RNA',
          size: '800MB',
          modifiedTime: '2024-03-19 16:45:00',
          status: '正常',
        },
        {
          name: 'WGS数据1',
          type: 'WGS',
          size: '2.5GB',
          modifiedTime: '2024-03-19 14:20:00',
          status: '正常',
        },
      ];

      // 根据筛选条件过滤数据
      if (filters.name) {
        fileList.value = fileList.value.filter((item) => item.name.toLowerCase().includes(filters.name.toLowerCase()));
      }

      if (filters.type !== 'all') {
        fileList.value = fileList.value.filter((item) => item.type === filters.type);
      }

      // 更新总数
      total.value = fileList.value.length;

      // 分页处理
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      fileList.value = fileList.value.slice(start, end);
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 监听筛选条件变化
  watch(
    filters,
    () => {
      currentPage.value = 1; // 重置页码
      fetchData();
    },
    { deep: true }
  );

  // 处理节点点击
  const handleNodeClick = (data: any) => {
    console.log(data);
    // 可以在这里添加根据选中节点重新加载数据的逻辑
    fetchData();
  };

  // 处理分页大小变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  // 处理页码变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  // 新建文件夹相关
  const newFolderDialogVisible = ref(false);
  const newFolderFormRef = ref<FormInstance>();
  const newFolderForm = reactive({
    name: '',
  });
  const newFolderRules = {
    name: [
      { required: true, message: '请输入文件夹名称', trigger: 'blur' },
      { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
    ],
  };

  // 处理新建文件夹
  const handleCreateFolder = async () => {
    if (!newFolderFormRef.value) return;

    await newFolderFormRef.value.validate(async (valid) => {
      if (valid) {
        try {
          // TODO: 调用后端API创建文件夹
          // 这里先模拟创建成功
          ElMessage.success('文件夹创建成功');
          newFolderDialogVisible.value = false;
          newFolderForm.name = '';
          // 刷新数据
          fetchData();
        } catch (error) {
          ElMessage.error('创建文件夹失败');
        }
      }
    });
  };

  // 上传数据相关
  const uploadDialogVisible = ref(false);
  const uploadFormRef = ref<FormInstance>();
  const uploadForm = reactive({
    type: '',
    files: [] as UploadUserFile[],
  });
  const uploadRules = {
    type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
    files: [{ required: true, message: '请选择要上传的文件', trigger: 'change' }],
  };

  // 处理文件变化
  const handleFileChange: UploadProps['onChange'] = (uploadFile) => {
    if (uploadFile.status === 'ready') {
      // 检查文件大小
      if (uploadFile.raw && uploadFile.raw.size > 10 * 1024 * 1024 * 1024) {
        ElMessage.error('文件大小不能超过10GB');
        return;
      }
      uploadForm.files.push(uploadFile);
    }
  };

  // 处理文件移除
  const handleFileRemove: UploadProps['onRemove'] = (uploadFile) => {
    const index = uploadForm.files.findIndex((file) => file.uid === uploadFile.uid);
    if (index !== -1) {
      uploadForm.files.splice(index, 1);
    }
  };

  // 处理上传
  const handleUpload = async () => {
    if (!uploadFormRef.value) return;

    await uploadFormRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (uploadForm.files.length === 0) {
            ElMessage.warning('请先选择要上传的文件');
            return;
          }

          // 创建 FormData 对象
          const formData = new FormData();
          formData.append('type', uploadForm.type);
          uploadForm.files.forEach((file) => {
            if (file.raw) {
              formData.append('files', file.raw);
            }
          });

          // TODO: 调用后端API上传文件
          // 这里先模拟上传成功
          ElMessage.success('文件上传成功');
          uploadDialogVisible.value = false;
          uploadForm.type = '';
          uploadForm.files = [];
          // 刷新数据
          fetchData();
        } catch (error) {
          ElMessage.error('文件上传失败');
        }
      }
    });
  };

  // 修改下拉菜单项点击事件
  const handleDropdownCommand = (command: string) => {
    if (command === 'folder') {
      newFolderForm.name = '';
      if (newFolderFormRef.value) {
        newFolderFormRef.value.resetFields();
      }
      newFolderDialogVisible.value = true;
    } else if (command === 'upload') {
      uploadForm.type = '';
      uploadForm.files = [];
      if (uploadFormRef.value) {
        uploadFormRef.value.resetFields();
      }
      uploadDialogVisible.value = true;
    } else if (command === 'workflow') {
      router.push({ name: 'ProjectWorkflow' });
    }
  };

  // 文件编辑相关
  const fileEditDialogVisible = ref(false);
  const currentFile = reactive<FileInfo>({
    name: '',
    path: '',
    type: '',
    id: '',
    size: '',
    creator: '',
    createTime: '',
    updateTime: '',
    tags: [],
    features: [],
  });

  const handleEditFile = (file: FileInfo) => {
    if (file.type === 'folder') return;
    Object.assign(currentFile, {
      name: file.name,
      path: '/path/to/file', // 这里需要根据实际情况设置
      type: file.type,
      id: 'FILE-123456', // 这里需要根据实际情况设置
      size: file.size,
      creator: 'admin', // 这里需要根据实际情况设置
      createTime: '2024-03-20 10:00:00', // 这里需要根据实际情况设置
      updateTime: '2024-03-20 10:00:00', // 这里需要根据实际情况设置
      tags: ['demo'],
      features: [{ name: 'demo', value: 'demo' }],
    });
    fileEditDialogVisible.value = true;
  };

  // 初始化加载数据
  fetchData();
</script>

<style scoped>
  .directory-tree {
    padding: 20px;
    height: 100%;
    width: 280px;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .file-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-container {
    margin-top: 20px;
    padding: 10px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
  }

  .el-select {
    min-width: 100px;
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
  }
</style>
