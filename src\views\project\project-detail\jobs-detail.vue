<template>
  <div class="flex flex-col h-full overflow-y-auto">
    <div class="pr-5 border-b border-#E4E7ED flex h-70px items-center">
      <h2 class="text-xl font-bold pl-5 flex gap-2 w-280px items-center">
        <el-icon><Loading /></el-icon>
        <div>
          <div class="line-clamp-1">BWA-MEM FASTQ Read</div>
          <div class="text-sm">等待</div>
        </div>
      </h2>

      <el-button :icon="VideoPlay" type="primary" @click="handleStartJob"> 启动新的作业 </el-button>
      <el-button :icon="Document" type="primary" @click="handleViewLog"> 查看日志 </el-button>
      <el-button :icon="Files" type="primary" @click="handleViewIO"> 查看输入输出 </el-button>
      <el-button :icon="InfoFilled" type="primary" @click="handleViewInfo"> 查看信息 </el-button>
      <el-button :icon="CircleClose" type="danger" @click="handleTerminateJob"> 终止作业 </el-button>
    </div>

    <div class="p-5 w-full" style="background-color: #f5f7fa">
      <div class="rounded bg-white flex w-full">
        <div class="pl-3 flex-1 w-0">
          <div class="text-sm text-tip flex gap-10 h-50px w-full whitespace-nowrap overflow-x-auto overflow-y-hidden">
            <div
              v-for="(time, index) in timePoints"
              :key="index"
              class="pt-2 flex-shrink-0"
              :class="{ 'pl-2 border-l border-#E4E7ED': index > 0 }"
            >
              {{ time }}
            </div>
          </div>

          <div class="mt-2 pb-2 flex w-full">
            <div class="i-uiw:loading"></div>
            <div class="font-bold ml-3 line-clamp-1">BWA-MEM FASTQ Read Mapper</div>
          </div>
        </div>

        <div class="text-right w-100px">
          <div class="text-#E6A23C leading-40px pr-2 border-r border-#E6A23C h-40px">当前</div>
          <div class="text-p mt-2 pr-2">--- log</div>
        </div>
      </div>

      <div class="mt-3 gap-5 grid grid-cols-2">
        <div>
          <h4 class="font-bold">输入</h4>
          <ul class="text-sm mt-2 flex flex-col gap-3">
            <li v-for="(item, index) in inputData" :key="index">
              <div class="text-tip">{{ item.label }}</div>
              <div class="text-p">{{ item.value }}</div>
            </li>
          </ul>
        </div>
        <div>
          <h4 class="font-bold">输出</h4>
          <div class="text-sm text-tip mt-2">No outputs</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Loading, VideoPlay, Document, Files, InfoFilled, CircleClose } from '@element-plus/icons-vue';
  import { ref } from 'vue';

  // 测试数据
  const timePoints = ref([
    '01:34:17 PM',
    '01:34:32 PM',
    '01:35:47 PM',
    '01:36:58 PM',
    '01:37:25 PM',
    '01:38:10 PM',
    '01:39:22 PM',
    '01:40:15 PM',
  ]);

  // 输入数据
  const inputData = ref([
    { label: 'Reads (reads_fastqgz)', value: 'SRR100022_20_1.fq.gz' },
    { label: 'Reads (right metas) (reads2_fastqgz)', value: 'SRR100022_20_2.fq.gz' },
    {
      label: 'BWA reference genome index (genomeindex_targz)',
      value: 'Reference Genome Files human gik v37.bwa-index.tar.gz',
    },
  ]);

  const handleStartJob = () => {
    console.log('启动新的作业');
    // 这里添加启动作业的逻辑
  };

  const handleViewLog = () => {
    console.log('查看日志');
    // 这里添加查看日志的逻辑
  };

  const handleViewIO = () => {
    console.log('查看输入输出');
    // 这里添加查看输入输出的逻辑
  };

  const handleViewInfo = () => {
    console.log('查看信息');
    // 这里添加查看信息的逻辑
  };

  const handleTerminateJob = () => {
    console.log('终止作业');
    // 这里添加终止作业的逻辑
  };
</script>
