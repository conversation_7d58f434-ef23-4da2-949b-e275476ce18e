<template>
  <div class="bg-bac h-full">
    <div class="p-5 h-full">
      <div class="rounded-md bg-white flex flex-col h-full">
        <div class="mb-4 px-5 pt-5 flex items-center">
          <el-button type="danger" @click="handleStopAllJobs" round :icon="CircleClose">中止所有作业</el-button>
        </div>

        <div class="mb-4 px-5 flex gap-4">
          <div>
            <el-input
              v-model="filters.searchKeyword"
              style="width: 300px"
              placeholder="搜索"
              :suffix-icon="Search"
              clearable
            />
          </div>

          <div>
            <el-select v-model="filters.state" placeholder="所有状态" clearable>
              <el-option
                v-for="item in stateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>

          <div>
            <el-select v-model="filters.user" placeholder="所有用户" clearable>
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>

          <div>
            <el-date-picker
              v-model="filters.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </div>
        </div>

        <div class="flex flex-1 flex-col h-0">
          <div class="px-5 flex-1 h-0">
            <el-table
              ref="multipleTableRef"
              class="c-table-header h-full"
              :data="tableData"
              style="width: 100%"
              highlight-current-row
            >
              <el-table-column prop="state" label="状态">
                <template #default="scope">
                  <div class="flex items-center">
                    <div class="status-dot mr-2" :class="getStatusClass(scope.row.state)"></div>
                    {{ scope.row.state }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" min-width="100px">
                <template #default="scope">
                  <a class="text-p cursor-pointer" @click="handleRowClick(scope.row)">{{ scope.row.name }}</a>
                </template>
              </el-table-column>
              <el-table-column prop="program" label="可执行程序" />
              <el-table-column prop="user" label="启动用户" />
              <el-table-column prop="startTime" label="开始时间" />
              <el-table-column prop="runTime" label="运行时间" />
              <el-table-column prop="price" label="价格" />
              <el-table-column prop="url" label="URL" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-popconfirm
                    v-if="['运行', '等待'].includes(scope.row.state)"
                    title="确定要中止作业吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="handleStopJob(scope.row)"
                  >
                    <template #reference>
                      <el-button type="danger" link>中止</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination-bottom">
            <el-pagination
              background
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { CircleClose, Search, Loading } from '@element-plus/icons-vue';
  import { ref, reactive, onBeforeMount, watch } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import jobsService from '@/api/jobs';
  import type { Job } from '@/api/jobs';

  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<Job[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);

  const filters = reactive({
    searchKeyword: '',
    name: '',
    state: '',
    user: '',
    time: [] as string[],
  });

  // 选项数据
  const stateOptions = [
    { label: '所有状态', value: '' },
    { label: '等待', value: '等待' },
    { label: '运行', value: '运行' },
    { label: '完成', value: '完成' },
  ];

  const userOptions = [
    { label: '所有用户', value: '' },
    { label: '用户1', value: 'user1' },
    { label: '用户2', value: 'user2' },
  ];

  // 处理中止单个作业
  const handleStopJob = (job: Job) => {
    console.log('中止作业:', job);
    jobsService.stopJob(job.id);
    ElMessage.success(`已成功中止作业 ${job.name}`);
  };

  const handleStopAllJobs = () => {
    ElMessageBox.confirm('确定要中止所有作业吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        jobsService.stopAllJobs();
        fetchData(); // 刷新数据
        ElMessage.success('已成功中止所有作业');
      })
      .catch(() => {
        // 用户取消操作，不需要处理
      });
  };

  // 状态样式
  const getStatusClass = (status: string) => {
    switch (status) {
      case '运行':
        return 'status-success';
      case '完成':
        return 'status-default';
      case '等待':
        return 'status-creating';
      case '创建中':
        return 'status-creating';
      default:
        return 'status-default';
    }
  };

  // 处理行点击
  const handleRowClick = (row: Job) => {
    console.log('行点击:', row);
    router.push({ name: 'ProjectJobsDetail', query: { id: row.id } });
  };

  // 分页处理
  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  // 获取表格数据
  async function fetchData() {
    try {
      loading.value = true;
      await new Promise((resolve) => setTimeout(resolve, 300)); // 模拟延迟

      // 使用JobsService获取过滤后的数据
      const filteredData = jobsService.getFilteredJobs(filters);

      // 更新总数
      total.value = filteredData.length;

      // 分页处理
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      tableData.value = filteredData.slice(start, end);
    } catch (error) {
      console.log(error);
      ElMessage.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 监听筛选条件变化
  watch(
    filters,
    () => {
      currentPage.value = 1; // 重置页码
      fetchData();
    },
    { deep: true }
  );

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });
</script>

<style lang="scss" scoped>
  .el-select {
    min-width: 100px;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-success {
    background-color: #67c23a;
  }

  .status-error {
    background-color: #f56c6c;
  }

  .status-default {
    background-color: #909399;
  }

  .status-creating {
    background-color: #e6a23c;
  }

  // 分页容器样式
  .pagination-container {
    padding: 10px;
    border-radius: 6px;
  }
</style>
