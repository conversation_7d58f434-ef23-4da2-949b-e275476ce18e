import type { RouteRecordRaw } from 'vue-router';

// 静态路由
export const staticRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/views/index.vue'),
    redirect: '/project',
    children: [
      {
        path: '/project',
        name: 'Project',
        component: () => import('@/views/project/index.vue'),
      },
      {
        path: '/project/detail',
        name: 'ProjectDetail',
        component: () => import('@/views/project/project-detail/index.vue'),
        redirect: '/project/detail/manage',
        props: true,
        children: [
          {
            path: 'settings',
            name: 'ProjectSettings',
            component: () => import('@/views/project/project-detail/settings.vue'),
            props: true,
          },

          {
            path: 'manage',
            name: 'ProjectManage',
            component: () => import('@/views/project/project-detail/manage.vue'),
            props: true,
          },
          {
            path: 'workflow',
            name: 'ProjectWorkflow',
            component: () => import('@/views/project/project-detail/workflow.vue'),
            props: true,
          },
          {
            path: 'jobs',
            name: 'ProjectJobs',
            component: () => import('@/views/project/project-detail/jobs.vue'),
            props: true,
          },
          {
            path: 'jobs-detail',
            name: 'ProjectJobsDetail',
            component: () => import('@/views/project/project-detail/jobs-detail.vue'),
            props: true,
          },
          {
            path: 'visualization',
            name: 'ProjectVisualization',
            component: () => import('@/views/project/project-detail/visualization.vue'),
            props: true,
          },
        ],
      },
      {
        path: '/data',
        name: 'Data',
        component: () => import('@/views/data/index.vue'),
      },
      {
        path: '/tools',
        name: 'Tools',
        component: () => import('@/views/tools/index.vue'),
      },
      {
        path: '/tools/tools-detail',
        name: 'ToolsDetail',
        component: () => import('@/views/tools/tools-detail.vue'),
        props: (route) => ({ id: route.query.id }),
      },
      {
        path: '/org',
        name: 'Org',
        component: () => import('@/views/org/index.vue'),
      },
      {
        path: '/help',
        name: 'Help',
        component: () => import('@/views/help/index.vue'),
      },
    ],
  },

  //登录注册
  {
    path: '/login',
    redirect: '/login/index',
    component: () => import('@/views/user/user.vue'),
    children: [
      {
        path: 'index',
        name: 'Login',
        component: () => import('@/views/user/login.vue'),
      },
    ],
  },
];
