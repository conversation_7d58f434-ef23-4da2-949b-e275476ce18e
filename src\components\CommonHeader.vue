<template>
  <div class="header-content">
    <div class="left-section">
      <div class="logo" @click="router.push('/')">IRAP</div>
      <div class="menu-container">
        <el-menu
          mode="horizontal"
          class="nav-menu"
          :ellipsis="false"
          :default-active="activeIndex"
          router
          @select="handleSelect"
        >
          <template v-for="(item, index) in menuItems">
            <el-sub-menu v-if="item.children" :index="item.path" :key="item.path + '-submenu'">
              <template #title>
                {{ item.name }}
              </template>
              <el-menu-item
                v-for="(child, cidx) in item.children"
                :key="child.path"
                :index="child.path"
                :route="child.path"
              >
                {{ child.name }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item v-else :key="item.path + '-item'" :index="item.path" :route="item.path">
              {{ item.name }}
            </el-menu-item>
          </template>
        </el-menu>
        <div class="triangle-indicator" :style="triangleStyle"></div>
      </div>
    </div>
    <div class="right-section">
      <el-input v-model="searchKeyword" placeholder="搜索..." :prefix-icon="Search" class="search-input" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch, nextTick } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();

  const menuItems = [
    { name: '项目', path: '/project' },
    { name: '数据', path: '/data' },
    {
      name: '工具',
      path: '/tools',
      children: [
        { name: '工具库列表', path: '/tools' },
        { name: 'Jupyterlab', path: '/tools/tool2' },
        { name: 'Rstudio', path: '/tools/tool3' },
      ],
    },
    { name: '管理', path: '/org' },
    { name: '帮助', path: '/help' },
  ];

  const getActiveIndex = (path: string) => {
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (path.startsWith(child.path)) {
            return item.path;
          }
        }
      }
      if (path.startsWith(item.path)) {
        return item.path;
      }
    }
    return path;
  };

  const activeIndex = ref(getActiveIndex(route.path));
  const searchKeyword = ref('');
  const menuItemWidths = ref<number[]>([]);
  const menuItemPositions = ref<number[]>([]);

  const triangleStyle = computed(() => {
    const index = menuItems.findIndex((item) => item.path === activeIndex.value);
    if (index >= 0 && menuItemPositions.value.length > index) {
      return {
        left: `${menuItemPositions.value[index] + menuItemWidths.value[index] / 2}px`,
        display: 'block',
      };
    }
    return { left: '0', display: 'none' };
  });

  const calculateMenuItemMetrics = () => {
    const menuEls = document.querySelectorAll(
      '.nav-menu > .el-menu-item, .nav-menu > .el-sub-menu > .el-sub-menu__title'
    );
    const container = document.querySelector('.menu-container');
    const menuLeft = container?.getBoundingClientRect().left || 0;
    const newWidths: number[] = [];
    const newPositions: number[] = [];

    menuEls.forEach((item) => {
      const rect = item.getBoundingClientRect();
      newWidths.push(rect.width);
      newPositions.push(rect.left - menuLeft);
    });

    menuItemWidths.value = newWidths;
    menuItemPositions.value = newPositions;
  };

  // 当路由变化时更新activeIndex
  watch(
    () => route.path,
    (newPath) => {
      activeIndex.value = getActiveIndex(newPath);
    }
  );

  const handleSelect = (index: string) => {
    activeIndex.value = index;
    router.push(index);
  };

  onMounted(() => {
    // 初始化activeIndex为当前路由
    activeIndex.value = getActiveIndex(route.path);
    nextTick(() => {
      calculateMenuItemMetrics();
    });
    window.addEventListener('resize', calculateMenuItemMetrics);
  });

  watch(activeIndex, () => {
    nextTick(() => {
      calculateMenuItemMetrics();
    });
  });
</script>

<style lang="scss" scoped>
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background-color: #007f99;
    color: #fff;
  }

  .left-section {
    display: flex;
    align-items: center;
  }

  .logo {
    font-size: 24px;
    font-weight: bold;
    margin-right: 20px;
    cursor: pointer;
  }

  .menu-container {
    position: relative;
  }

  .nav-menu {
    --el-menu-border-color: transparent;
    --el-menu-bg-color: transparent;
    --el-menu-active-color: #fff;
    --el-menu-text-color: #fff;
    --el-menu-hover-text-color: #fff;
  }

  :deep(.el-menu),
  :deep(.el-menu-item),
  :deep(.el-sub-menu),
  :deep(.el-sub-menu__title) {
    border-bottom: none !important;
  }

  .el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: none;
    color: #fff;
  }

  .el-menu--horizontal > .el-menu-item:hover {
    color: #fff;
  }

  .triangle-indicator {
    position: absolute;
    bottom: 0;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    transition: left 0.3s ease-in-out;
  }

  .search-input {
    width: 200px;
  }

  .right-section {
    display: flex;
    align-items: center;
  }

  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover,
  .el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
    background-color: transparent;
  }

  :deep(.el-sub-menu__title.is-active) {
    border-bottom: none !important;
  }

  :deep(.el-menu--horizontal > .el-sub-menu.is-active .el-sub-menu__title) {
    border-bottom: none !important;
  }
</style>
