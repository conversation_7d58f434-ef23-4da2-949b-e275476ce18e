<template>
  <div class="h-full bg-bac p-5">
    <div v-loading="loading" class="project-list rounded-md">
      <div class="flex items-center px-5 py-4 pt-5">
        <!-- <h2 class="text-2xl mr-5">项目</h2>

        <el-menu class="bottom--1px relative" mode="horizontal" :ellipsis="false" default-active="1" router>
          <el-menu-item index="1"> 所有 </el-menu-item>
        </el-menu> -->

        <el-button type="primary" :icon="Plus" @click="onAdd">新建项目</el-button>
        <div class="ml-auto flex gap-4">
          <div>
            <el-input
              style="width: 300px"
              v-model="filters.name"
              placeholder="请输入关键字搜索"
              clearable
              :suffix-icon="Search"
            ></el-input>
          </div>

          <!-- <div>
            <el-select v-model="filters.user" placeholder="所有用户" clearable>
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div> -->

          <div>
            <el-date-picker
              v-model="filters.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </div>
        </div>
      </div>

      <div class="h-0 flex flex-1 flex-col">
        <div class="h-0 flex-1 px-5">
          <el-table
            ref="multipleTableRef"
            class="c-table-header h-full"
            :data="tableData"
            style="width: 100%"
            highlight-current-row
          >
            <el-table-column prop="name" label="名称" min-width="100px">
              <template #default="scope">
                <a class="cursor-pointer text-p" @click="handleRowClick(scope.row)">{{ scope.row.name }}</a>
              </template>
            </el-table-column>
            <el-table-column prop="dataUsage" label="数据量" />
            <el-table-column prop="state" label="状态">
              <template #default="scope">
                <div class="flex items-center">
                  <div
                    v-if="scope.row.state !== '创建中'"
                    class="status-dot mr-2"
                    :class="getStatusClass(scope.row.state)"
                  ></div>
                  <el-icon v-if="scope.row.state === '创建中'" class="mr-1"><Loading /></el-icon>
                  {{ scope.row.state }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" link @click="handleDetail(scope.row)"> 详情 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-bottom">
          <el-pagination
            background
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 右侧详情面板 -->
    <div class="project-detail" :class="{ show: selectedProject }" ref="projectDetailRef" @click.stop>
      <template v-if="selectedProject">
        <div class="detail-header">
          <h3 class="text-xl font-bold">{{ selectedProject.name }}</h3>
          <el-button type="primary" link @click="handleEdit(selectedProject)">编辑</el-button>
        </div>

        <div class="detail-content">
          <div class="detail-section">
            <h4 class="section-title">基本信息</h4>
            <div class="info-item">
              <span class="label">数据量</span>
              <span class="value">{{ selectedProject.dataUsage }}</span>
            </div>
            <div class="info-item">
              <span class="label">状态</span>
              <span class="value">
                <div class="flex items-center">
                  <div
                    v-if="selectedProject.state !== '创建中'"
                    class="status-dot mr-2"
                    :class="getStatusClass(selectedProject.state)"
                  ></div>
                  <el-icon v-if="selectedProject.state === '创建中'" class="mr-1"><Loading /></el-icon>
                  {{ selectedProject.state }}
                </div>
              </span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 新建项目弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form :model="projectForm" label-width="130px" :rules="rules" ref="projectFormRef">
        <!-- 第一部分：基本信息 -->
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="projectForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="default" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { Plus, Loading, Search } from '@element-plus/icons-vue';
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { onClickOutside } from '@vueuse/core';
  import RegionCascader from '@/components/RegionCascader.vue';
  import AccountSelect from '@/components/AccountSelect.vue';
  const router = useRouter();

  // 1. 类型定义和接口
  interface Project {
    id: number;
    name: string;
    dataUsage: string;
    access: string;
    members: string;
    state: string;
    payAccount: string;
    tags: string;
  }

  // 2. 响应式数据
  const loading = ref(false);
  const tableData = ref<Project[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(30);
  const selectedProject = ref<Project | null>(null);
  const projectDetailRef = ref<HTMLElement | null>(null);

  // 3. 表单相关
  const dialogVisible = ref(false);
  const dialogTitle = ref('新建项目');
  const isEdit = ref(false);
  const currentEditIndex = ref(-1);
  const projectFormRef = ref();
  const memberLoading = ref(false);
  const memberOptions = ref<Array<{ id: number; name: string }>>([]);

  const projectForm = reactive({
    name: '',
    tags: '',
    members: [] as number[],
    idatavisId: '',
    copyData: false,
    payAccount: '',
    region: [] as string[],
    copy: 'all',
    delete: 'all',
    download: 'all',
    remark: '',
  });

  const rules = ref({
    name: [{ required: true, message: '请输入项目名称' }],
    tags: [{ required: true, message: '请输入项目标签' }],
    members: [{ required: true, message: '请选择成员' }],
    idatavisId: [{ required: true, message: '请输入IDATAVIS应用ID' }],
    payAccount: [{ required: true, message: '请选择付费账户' }],
    region: [{ required: true, message: '请选择地区' }],
  });

  // 4. 表格相关
  const filters = reactive({
    name: '',
    id: '',
    user: '',
    time: [] as string[],
  });

  const userOptions = [
    { label: '所有用户', value: '' },
    { label: '用户1', value: 'user1' },
    { label: '用户2', value: 'user2' },
  ];

  const tagOptions = ref([
    { label: 'WES', value: 'WES' },
    { label: 'RNA', value: 'RNA' },
    { label: 'WGS', value: 'WGS' },
    { label: '测序', value: '测序' },
    { label: '分析', value: '分析' },
  ]);

  const accessOptions = ref([
    {
      value: 1,
      label: 'Admin',
    },
  ]);

  // 6. 工具方法
  const getStatusClass = (status: string) => {
    switch (status) {
      case '运行中':
        return 'status-success';
      case '已停止':
        return 'status-error';
      case '创建中':
        return 'status-creating';
      default:
        return 'status-default';
    }
  };

  const resetForm = () => {
    if (projectFormRef.value) {
      projectFormRef.value.resetFields();
    }
    projectForm.name = '';
    projectForm.tags = '';
    projectForm.members = [];
    projectForm.idatavisId = '';
    projectForm.copyData = false;
    projectForm.payAccount = '';
    projectForm.region = [];
    projectForm.copy = 'all';
    projectForm.delete = 'all';
    projectForm.download = 'all';
    projectForm.remark = '';
  };

  const handleMemberSearch = async (query: string) => {
    if (query) {
      memberLoading.value = true;
      try {
        // 这里应该调用后端API进行成员搜索
        // 示例：const response = await searchMembers(query);
        // memberOptions.value = response.data;

        // 临时模拟数据
        memberOptions.value = [
          { id: 1, name: '张三' },
          { id: 2, name: '李四' },
          { id: 3, name: '王五' },
          { id: 4, name: '张小' },
        ].filter((item) => item.name.includes(query));
      } catch (error) {
        console.error('搜索成员失败:', error);
      } finally {
        memberLoading.value = false;
      }
    } else {
      memberOptions.value = [];
    }
  };

  const submitForm = () => {
    // 这里处理表单提交逻辑
    console.log('提交的项目信息:', projectForm);

    // 获取地区文本信息用于展示
    let regionText = '';
    if (projectForm.region && projectForm.region.length > 0) {
      regionText = projectForm.region[projectForm.region.length - 1];
    }

    const newProject = {
      id: tableData.value.length + 1,
      name: projectForm.name,
      dataUsage: '0GB',
      access: 'Private',
      members: '1',
      state: '运行中',
      payAccount: projectForm.payAccount || 'account' + (tableData.value.length + 1),
      tags: Array.isArray(projectForm.tags) ? projectForm.tags.join(',') : projectForm.tags,
    };

    if (isEdit.value && currentEditIndex.value !== -1) {
      // 编辑现有项目
      tableData.value[currentEditIndex.value] = newProject;
      ElMessage.success('编辑成功');
    } else {
      // 添加新项目
      tableData.value.push(newProject);
      ElMessage.success('创建成功');
    }

    // 关闭弹窗
    dialogVisible.value = false;
  };

  const handleEdit = (row: Project) => {
    dialogTitle.value = '编辑项目';
    isEdit.value = true;
    currentEditIndex.value = tableData.value.findIndex((item) => item.id === row.id);

    // 重置表单
    resetForm();

    // 填充表单数据
    projectForm.name = row.name;
    projectForm.tags = row.tags; // 直接使用原始tags字符串
    projectForm.members = [1]; // 这里需要根据实际情况处理
    projectForm.idatavisId = '';
    projectForm.copyData = false;
    projectForm.payAccount = row.payAccount;
    projectForm.region = [];
    projectForm.copy = 'all';
    projectForm.delete = 'all';
    projectForm.download = 'all';
    projectForm.remark = '';

    dialogVisible.value = true;
  };

  const handleDelete = (row: Project) => {
    const index = tableData.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      tableData.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  };

  const handleDetail = (row: Project) => {
    router.push({
      path: `/project/detail`,
      query: {
        id: row.id,
      },
    });
  };

  const onAdd = () => {
    dialogTitle.value = '新建项目';
    isEdit.value = false;
    currentEditIndex.value = -1;

    // 重置表单
    resetForm();

    dialogVisible.value = true;
  };

  const handleRowClick = (row: Project) => {
    selectedProject.value = row;
  };

  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  async function fetchData() {
    try {
      loading.value = true;
      // const { data } = await dataBrowse();
      await new Promise((resolve) => setTimeout(resolve, 300));
      tableData.value = [
        {
          id: 1,
          name: 'SC_WES',
          dataUsage: '1.2TB',
          access: 'Admin',
          members: '1',
          state: '创建中',
          payAccount: 'account1',
          tags: 'WES,测序',
        },
        {
          id: 2,
          name: 'SC_RNA',
          dataUsage: '800GB',
          access: 'Uploader',
          members: '3',
          state: '已停止',
          payAccount: 'account2',
          tags: 'RNA,分析',
        },
        {
          id: 3,
          name: 'SC_WGS',
          dataUsage: '2.5TB',
          access: 'Viewer',
          members: '2',
          state: '运行中',
          payAccount: 'account3',
          tags: 'WGS,测序',
        },
        {
          id: 4,
          name: 'TCGA_WES',
          dataUsage: '3.8TB',
          access: 'Admin',
          members: '5',
          state: '运行中',
          payAccount: 'account4',
          tags: 'WES,癌症',
        },
        {
          id: 5,
          name: 'TCGA_RNA',
          dataUsage: '2.1TB',
          access: 'Viewer',
          members: '4',
          state: '已停止',
          payAccount: 'account5',
          tags: 'RNA,癌症',
        },
      ];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // 7. 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });

  // 监听 filters 对象的变化
  watch(
    filters,
    () => {
      currentPage.value = 1; // 重置页码
      fetchData();
    },
    { deep: true }
  );

  onClickOutside(projectDetailRef, () => {
    selectedProject.value = null;
  });
</script>

<style lang="scss" scoped>
  .el-select {
    min-width: 100px;
  }

  .el-cascader {
    width: 100%;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-success {
    background-color: #67c23a;
  }

  .status-error {
    background-color: #f56c6c;
  }

  .status-default {
    background-color: #909399;
  }

  .status-creating {
    background-color: #e6a23c;
  }

  // 新增样式
  .form-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ebeef5;

    &:last-child {
      border-bottom: none;
    }
  }

  .form-section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }

  .project-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
  }

  .project-detail {
    width: 400px;
    background-color: #fff;
    overflow-y: auto;
    padding: 20px;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    // 添加过渡动画
    transition: transform 0.3s ease;
    transform: translateX(100%);

    &.show {
      transform: translateX(0);
    }

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
    }

    .detail-content {
      .detail-section {
        margin-bottom: 25px;

        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #333;
        }

        .info-item {
          display: flex;
          margin-bottom: 12px;
          line-height: 1.5;

          .label {
            width: 80px;
            color: #909399;
          }

          .value {
            flex: 1;
            color: #333;
          }
        }

        .tags-container {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
  }
</style>
